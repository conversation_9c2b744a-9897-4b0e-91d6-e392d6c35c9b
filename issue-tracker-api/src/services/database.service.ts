import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { Pool } from "pg";
import {
    GroupedStatisticsDto,
    IssueDetailsResponseDto,
    IssueListItemDto,
    StatisticIssuesResponseDto,
    StatisticType,
} from "../dto";
import {
    AggregatedDataRow,
    IssueDetailsRow,
    IssueListRow,
    StatisticRow,
    StatusProgressRow,
    TimelineRow,
} from "../types";
import { DateRange } from "../types/date-range.type";

@Injectable()
export class DatabaseService {
    private pool: Pool;

    constructor(private configService: ConfigService) {
        this.pool = new Pool({
            host: this.configService.get<string>("DB_HOST", "localhost"),
            port: this.configService.get<number>("DB_PORT", 5432),
            database: this.configService.get<string>("DB_NAME", "postgres"),
            user: this.configService.get<string>("DB_USER", "postgres"),
            password: this.configService.get<string>("DB_PASSWORD", "password"),
            max: 20,
            idleTimeoutMillis: 30000,
            connectionTimeoutMillis: 2000,
        });
    }

    async getStatistics(start: Date, end: Date): Promise<GroupedStatisticsDto> {
        try {
            // Date range parameters accepted but not yet applied to queries
            const [devResult, qaBugResult, qaTestingResult] = await Promise.all(
                [
                    this.getTransitionDuration(
                        "pullrequest:created",
                        [
                            "pullrequest:approved",
                            "pullrequest:changes_request_created",
                        ],
                        [start, end],
                        960,
                        "avg_time_for_code_review"
                    ),
                    this.getTimeInStatus(
                        "For QA",
                        [start, end],
                        "avg_time_for_bug_for_qa_to_todo",
                        960
                    ),
                    this.getTimeInStatus(
                        "In Test",
                        [start, end],
                        "avg_time_for_testing",
                        960
                    ),
                ]
            );

            return {
                dev: devResult.rows,
                qa: [...qaBugResult.rows, ...qaTestingResult.rows],
            };
        } catch (error: unknown) {
            const errorMessage =
                error instanceof Error
                    ? error.message
                    : "Unknown error occurred";
            console.error("Error executing statistics query:", errorMessage);
            throw error;
        }
    }

    async getStatisticIssues(
        statisticType: StatisticType,
        start?: Date | null,
        end?: Date | null
    ): Promise<StatisticIssuesResponseDto> {
        try {
            // TODO: Implement the actual database query based on statistic type
            // This is a placeholder implementation

            // For now, return a mock response structure
            const mockResponse: StatisticIssuesResponseDto = {
                statistic_type: statisticType,
                statistic_name: this.getStatisticDisplayName(statisticType),
                total_issues: 0,
                average_value: 0,
                formatted_average_value: "0 minutes",
                target_time: this.getTargetTime(statisticType),
                formatted_target_time: this.formatTargetTime(statisticType),
                issues: [],
            };

            console.log(
                `TODO: Implement getStatisticIssues for type: ${statisticType}`
            );
            console.log(
                `Date range: ${start?.toISOString()} to ${end?.toISOString()}`
            );

            return mockResponse;
        } catch (error: unknown) {
            const errorMessage =
                error instanceof Error
                    ? error.message
                    : "Unknown error occurred";
            console.error(
                "Error executing statistic issues query:",
                errorMessage
            );
            throw error;
        }
    }

    private getStatisticDisplayName(statisticType: StatisticType): string {
        switch (statisticType) {
            case "avg_time_for_code_review":
                return "Average Time in Code Review";
            case "avg_time_for_bug_for_qa_to_todo":
                return "Average Time for Bug QA to Todo";
            case "avg_time_for_testing":
                return "Average Time for Testing";
            default:
                return statisticType;
        }
    }

    private getTargetTime(statisticType: StatisticType): number {
        switch (statisticType) {
            case "avg_time_for_code_review":
                return 960; // 16 hours in minutes
            case "avg_time_for_bug_for_qa_to_todo":
                return 960; // 16 hours in minutes
            case "avg_time_for_testing":
                return 960; // 16 hours in minutes
            default:
                return 960;
        }
    }

    private formatTargetTime(statisticType: StatisticType): string {
        const targetMinutes = this.getTargetTime(statisticType);
        const hours = Math.floor(targetMinutes / 60);
        const minutes = targetMinutes % 60;

        if (hours > 0 && minutes > 0) {
            return `${hours} hours ${minutes} minutes`;
        } else if (hours > 0) {
            return `${hours} hours`;
        } else {
            return `${minutes} minutes`;
        }
    }

    async getIssuesList(
        start?: Date | null,
        end?: Date | null
    ): Promise<IssueListItemDto[]> {
        // Date range parameters accepted but not yet applied to queries
        const query = `
      WITH latest_jira_data AS (
          -- Get the most recent Jira event for each issue to extract summary and assignee
          SELECT DISTINCT ON (issue)
              issue,
              issue_type,
              issue_priority,
              issue_priority_icon,
              status,
              issue_summary,
              assignee_name,
              created_at
          FROM events 
          WHERE source = 'jira' 
            AND issue IS NOT NULL
            AND data IS NOT NULL
          ORDER BY issue, created_at DESC
      ),
      pr_counts AS (
          -- Count unique pull requests per issue from Bitbucket events
          SELECT 
              issue,
              COUNT(*) as pr_count
          FROM events 
          WHERE source = 'bitbucket' 
            and event_type ='pullrequest:created'
          GROUP BY issue
      ),
      latest_updates AS (
          -- Get the most recent update timestamp from either Jira or Bitbucket
          SELECT 
              issue,
              MAX(created_at) as last_updated
          FROM events 
          WHERE issue IS NOT NULL
          GROUP BY issue
      )
      SELECT 
          ljd.issue,
          ljd.issue_type,
          ljd.issue_priority,
          ljd.issue_priority_icon,
          ljd.status,
          ljd.issue_summary,
          ljd.assignee_name,
          COALESCE(pc.pr_count, 0) as prs,
          lu.last_updated as updated_at
      FROM latest_jira_data ljd
      LEFT JOIN pr_counts pc ON ljd.issue = pc.issue
      LEFT JOIN latest_updates lu ON ljd.issue = lu.issue
      ORDER BY lu.last_updated DESC;
    `;

        try {
            const result = await this.pool.query<IssueListRow>(query);
            return result.rows;
        } catch (error: unknown) {
            const errorMessage =
                error instanceof Error
                    ? error.message
                    : "Unknown error occurred";
            console.error("Error executing issues list query:", errorMessage);
            throw error;
        }
    }

    async getIssueDetails(issueKey: string): Promise<IssueDetailsResponseDto> {
        try {
            // Get issue details
            const issueDetailsQuery = `
        SELECT DISTINCT ON (issue)
            issue_type,
            issue,
            issue_summary,
            assignee_name,
            status,
            issue_priority,
            issue_priority_icon
        FROM events
        WHERE source = 'jira' AND issue = $1
        ORDER BY issue, created_at DESC;
      `;

            // Get aggregated actors and events data
            const aggregatedDataQuery = `
        SELECT
            json_agg(DISTINCT actor_name) AS actors,
            json_agg(DISTINCT event_type) AS events
        FROM events
        WHERE issue = $1;
      `;

            // Get timeline events
            const timelineQuery = `
        SELECT
            event_type,
            summarize_minutes(calculate_working_minutes(LAG(created_at, 1) OVER (PARTITION BY issue ORDER BY created_at), created_at)) AS elapsed_time,
            source,
            actor_name,
            repository,
            pr_title,
            pr_link,
            pr_id,
            pr_is_draft,
            is_pr_branch_closed,
            branch_name,
            issue_changelog,
            created_at 
        FROM events
        WHERE issue = $1
        ORDER BY created_at DESC;
      `;

            // Get status progress
            const statusProgressQuery = `
        WITH status_periods AS (
          SELECT
            issue,
            status,
            created_at AS started_at,
            -- Get the timestamp of the next status change for the same issue
            COALESCE(
              LEAD(created_at, 1) OVER (PARTITION BY issue ORDER BY created_at),
              NOW() -- For the current status, assume it ends now
            ) AS ended_at
          FROM events
          WHERE
            -- Ensure we only consider records that represent a status
            status IS NOT NULL AND status <> '' AND issue = $1 AND source = 'jira'
        )
        SELECT
          issue,
          status,
          summarize_minutes(
            SUM(calculate_working_minutes(started_at, ended_at))
          ) AS time_in_status
        FROM status_periods
        GROUP BY issue, status
        ORDER BY issue, MIN(started_at);
      `;

            const [
                issueDetailsResult,
                aggregatedDataResult,
                timelineResult,
                statusProgressResult,
            ] = await Promise.all([
                this.pool.query<IssueDetailsRow>(issueDetailsQuery, [issueKey]),
                this.pool.query<AggregatedDataRow>(aggregatedDataQuery, [
                    issueKey,
                ]),
                this.pool.query<TimelineRow>(timelineQuery, [issueKey]),
                this.pool.query<StatusProgressRow>(statusProgressQuery, [
                    issueKey,
                ]),
            ]);

            if (issueDetailsResult.rows.length === 0) {
                throw new Error(`Issue with key ${issueKey} not found`);
            }

            const issueDetails = issueDetailsResult.rows[0];
            const aggregatedData = aggregatedDataResult.rows[0];
            const timeline = timelineResult.rows;
            const statusProgress = statusProgressResult.rows;

            return {
                issue_details: {
                    issue_key: issueDetails.issue,
                    issue_type: issueDetails.issue_type,
                    issue_summary: issueDetails.issue_summary,
                    assignee_name: issueDetails.assignee_name,
                    current_status: issueDetails.status,
                    issue_priority: issueDetails.issue_priority,
                    issue_priority_icon: issueDetails.issue_priority_icon,
                },
                timeline: timeline,
                status_progress: statusProgress,
                filters: {
                    actors: aggregatedData.actors || [],
                    events: aggregatedData.events || [],
                },
            };
        } catch (error: unknown) {
            const errorMessage =
                error instanceof Error
                    ? error.message
                    : "Unknown error occurred";
            console.error("Error executing issue details query:", errorMessage);
            throw error;
        }
    }

    async onModuleDestroy() {
        await this.pool.end();
    }

    async getTimeInStatus(
        status: string,
        [start, end]: DateRange,
        statName: string,
        targetTimeInMinutes: number
    ) {
        const query = `
            WITH status_transition_time AS (
                SELECT
                    issue
                    , created_at
                    , status
                    ,
                    -- Get the timestamp of the next event for this issue
                    LEAD(created_at) OVER (PARTITION BY issue ORDER BY created_at) AS next_event_time
                FROM
                    events
                WHERE
                    source = 'jira'
                ORDER BY
                    issue
                    , created_at
            )
            , status_periods AS (
                SELECT
                    issue
                    , created_at AS entered_status
                    , next_event_time
                    ,
                    -- Calculate working minutes between this event and the next event (or current time)
                    calculate_working_minutes (created_at
                        , COALESCE(next_event_time, NOW())) AS minutes_in_period
                FROM
                    status_transition_time
                WHERE
                    status = $1 
                and TSTZRANGE(created_at, COALESCE(next_event_time, NOW()), '[]') && $2::tstzrange
            )
            , total_time_per_issue AS (
                SELECT
                    issue
                    , SUM(minutes_in_period) AS total_minutes_in_status
                    , COUNT(*) AS status_periods
                FROM
                    status_periods
                GROUP BY
                    issue
            )
            SELECT
                COUNT(distinct issue) AS total_issues
                , ROUND(AVG(total_minutes_in_status), 2) AS "value"
                , $3 AS target_time
                , $4 AS stat_type
            FROM
                total_time_per_issue;
    `;
        return this.pool.query<StatisticRow>(query, [
            status,
            `[${start.toISOString()}, ${end.toISOString()}]`,
            targetTimeInMinutes,
            statName,
        ]);
    }

    async getTransitionDuration(
        fromStatus: string,
        toStatuses: string[],
        dateRange: DateRange,
        targetTimeInMinutes: number,
        statName: string
    ) {
        const query = `
            WITH status_events AS (
                SELECT
                    issue
                    , created_at
                    , LOWER(COALESCE(status
                            , event_type)) AS status_lower
                FROM
                    events
                WHERE
                    pr_is_draft ISNULL
                    OR pr_is_draft = FALSE
                ORDER BY
                    issue
                    , status
                    , created_at
            )
            , from_status AS (
                SELECT
                    issue
                    , created_at AS from_time
                    , status_lower
                FROM
                    status_events
                WHERE
                    status_lower = $1
            )
            , to_status AS (
                SELECT DISTINCT ON (issue)
                    issue
                    , created_at AS to_time
                    , status_lower
                FROM
                    status_events
                WHERE
                    status_lower = ANY ($2::text[])
                ORDER BY
                    issue
                    , created_at
            )
            , issue_transitions AS (
                SELECT
                    fs.issue
                    , fs.status_lower from_status
                    , ts.status_lower to_status
                    , fs.from_time
                    , COALESCE(ts.to_time, NOW()) to_time
                FROM
                    from_status fs
                    JOIN to_status ts ON ts.issue = fs.issue
                WHERE
                    fs.from_time <= COALESCE(ts.to_time
                        , NOW())
                    AND TSTZRANGE(fs.from_time
                        , COALESCE(ts.to_time
                            , NOW()), '[]') && $3::tstzrange
            )
            , transition_durations AS (
                SELECT
                    issue
                    , from_time
                    , to_time
                    , calculate_working_minutes (from_time
                        , to_time) AS transition_duration
                FROM
                    issue_transitions
                WHERE
                    to_time IS NOT NULL
            )
            , work_time_by_issue AS (
                SELECT
                    issue
                    , SUM(transition_duration) transition_duration
                FROM
                    transition_durations
                GROUP BY
                    issue
            )
            SELECT
                COUNT(*) AS total_issues
                , ROUND(AVG(transition_duration) , 2) AS "value"
                , $4 AS target_time
                , $5 AS stat_type
            FROM
                work_time_by_issue;
            `;
        return this.pool.query<StatisticRow>(query, [
            fromStatus,
            toStatuses,
            `[${dateRange[0].toISOString()}, ${dateRange[1].toISOString()}]`,
            statName,
            targetTimeInMinutes,
        ]);
    }
}
